run:
  timeout: 5m
  allow-parallel-runners: true
formatters:
  enable:
    - gci
  settings: # please keep this alphabetized
    gci:
      sections:
        - standard
        - default
        - prefix(go.etcd.io)
        - skipGenerated
issues:
  # don't skip warning about doc comments
  # don't exclude the default set of lint
  exclude-use-default: false
  # restore some of the defaults
  # (fill in the rest as needed)
  exclude-rules:
    - path: "api/*"
      linters:
        - lll
    - path: "internal/*"
      linters:
        - dupl
        - lll
linters:
  disable-all: true
  enable:
    - copyloopvar
    - dupl
    - errcheck
    - goconst
    - gocyclo
    - gofmt
    - goimports
    - gosimple
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - prealloc
    - revive
    - staticcheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - usetesting

linters-settings:
  gci:
    skipGenerated: true
    preserveComments: true
    customOrder:
      - std
      - default
      - prefix(go.etcd.io)
  goimports:
    local-prefixes: go.etcd.io # Put imports beginning with prefix after 3rd-party packages.
  revive:
    rules:
      - name: comment-spacings
  gocyclo:
    min-complexity: 35
