/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	"net"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// EtcdClusterSpec defines the desired state of EtcdCluster.
type EtcdClusterSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// Size is the expected size of the etcd cluster.
	// +kubebuilder:validation:Minimum=1
	Size int `json:"size"`
	// Version is the expected version of the etcd container image.
	Version string `json:"version"`
	// StorageSpec is the name of the StorageSpec to use for the etcd cluster. If not provided, then each POD just uses the temporary storage inside the container.
	StorageSpec *StorageSpec `json:"storageSpec,omitempty"`
	// TLS is the TLS certificate configuration to use for the etcd cluster and etcd operator.
	TLS *TLSCertificate `json:"tls,omitempty"`
	// etcd configuration options are passed as command line arguments to the etcd container, refer to etcd documentation for configuration options applicable for the version of etcd being used.
	EtcdOptions []string `json:"etcdOptions,omitempty"`
}

type TLSCertificate struct {
	Provider    string         `json:"provider,omitempty"` // Defaults to Auto provider if not present
	ProviderCfg ProviderConfig `json:"providerCfg,omitempty"`
}

type ProviderConfig struct {
	AutoCfg        *ProviderAutoConfig        `json:"autoCfg,omitempty"`
	CertManagerCfg *ProviderCertManagerConfig `json:"certManagerCfg,omitempty"`
}

type AltNames struct {
	// DNSNames is the expected array of DNS subject alternative names.
	// if empty defaults to $(POD_NAME).$(ETCD_CLUSTER_NAME).$(POD_NAMESPACE).svc.cluster.local
	// +optional
	DNSNames []string `json:"dnsNames,omitempty"`

	// IPs is the expected array of IP address subject alternative names.
	// +optional
	IPs []net.IP `json:"ipAddresses,omitempty"`
}

type CommonConfig struct {
	// CommonName is the expected common name X509 certificate subject attribute.
	// Should have a length of 64 characters or fewer to avoid generating invalid CSRs.
	// +optional
	CommonName string `json:"commonName,omitempty"`

	// Organization is the expected array of Organization names to be used on the Certificate.
	// +optional
	Organization []string `json:"organizations,omitempty"`

	// AltNames contains the domain names and IP addresses that will be added
	// to the x509 certificate SubAltNames fields. The values will be passed
	// directly to the x509.Certificate object.
	AltNames AltNames `json:"altNames,omitempty"`

	// ValidityDuration is the expected duration until which the certificate will be valid,
	// expects in human-readable duration: 100d12h, if empty defaults to 90d
	// +optional
	ValidityDuration string `json:"validityDuration,omitempty"`

	// CABundleSecret is the expected secret name with CABundle present. It's used
	// by each etcd POD to verify TLS communications with its peers or clients. If it isn't
	// provided, the CA included in the secret generated by certificate provider will be
	// used instead if present; otherwise, there is no way to verify TLS communications.
	// +optional
	CABundleSecret string `json:"caBundleSecret,omitempty"`
}

type ProviderAutoConfig struct {
	// CommonConfig is the struct of common fields required to create a certificate
	CommonConfig `json:",inline"`
}

type ProviderCertManagerConfig struct {
	// CommonConfig is the struct of common fields required to create a certificate
	CommonConfig `json:",inline"`

	// IssuerKind is the expected kind of Issuer, either "ClusterIssuer" or "Issuer"
	IssuerKind string `json:"issuerKind"`

	// IssuerName is the expected name of Issuer required to issue a certificate
	IssuerName string `json:"issuerName"`
}

// EtcdClusterStatus defines the observed state of EtcdCluster.
type EtcdClusterStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file
}

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status

// EtcdCluster is the Schema for the etcdclusters API.
type EtcdCluster struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   EtcdClusterSpec   `json:"spec,omitempty"`
	Status EtcdClusterStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// EtcdClusterList contains a list of EtcdCluster.
type EtcdClusterList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []EtcdCluster `json:"items"`
}

type StorageSpec struct {
	AccessModes       corev1.PersistentVolumeAccessMode `json:"accessModes,omitempty"`      // `ReadWriteOnce` (default) or `ReadWriteMany`. Note that `ReadOnlyMany` isn't allowed.
	StorageClassName  string                            `json:"storageClassName,omitempty"` // optional, the default one will be used if not specified
	PVCName           string                            `json:"pvcName,omitempty"`          // optional, only used when access mode is ReadWriteMany
	VolumeSizeRequest resource.Quantity                 `json:"volumeSizeRequest"`          // required.
	VolumeSizeLimit   resource.Quantity                 `json:"volumeSizeLimit,omitempty"`  // optional
}

func init() {
	SchemeBuilder.Register(&EtcdCluster{}, &EtcdClusterList{})
}
