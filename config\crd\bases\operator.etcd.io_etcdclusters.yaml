---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
  name: etcdclusters.operator.etcd.io
spec:
  group: operator.etcd.io
  names:
    kind: EtcdCluster
    listKind: EtcdClusterList
    plural: etcdclusters
    singular: etcdcluster
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: EtcdCluster is the Schema for the etcdclusters API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: EtcdClusterSpec defines the desired state of EtcdCluster.
            properties:
              etcdOptions:
                description: etcd configuration options are passed as command line
                  arguments to the etcd container, refer to etcd documentation for
                  configuration options applicable for the version of etcd being used.
                items:
                  type: string
                type: array
              size:
                description: Size is the expected size of the etcd cluster.
                minimum: 1
                type: integer
              storageSpec:
                description: StorageSpec is the name of the StorageSpec to use for
                  the etcd cluster. If not provided, then each POD just uses the temporary
                  storage inside the container.
                properties:
                  accessModes:
                    type: string
                  pvcName:
                    type: string
                  storageClassName:
                    type: string
                  volumeSizeLimit:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  volumeSizeRequest:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                required:
                - volumeSizeRequest
                type: object
              tls:
                description: TLS is the TLS certificate configuration to use for the
                  etcd cluster and etcd operator.
                properties:
                  provider:
                    type: string
                  providerCfg:
                    properties:
                      autoCfg:
                        properties:
                          altNames:
                            description: |-
                              AltNames contains the domain names and IP addresses that will be added
                              to the x509 certificate SubAltNames fields. The values will be passed
                              directly to the x509.Certificate object.
                            properties:
                              dnsNames:
                                description: |-
                                  DNSNames is the expected array of DNS subject alternative names.
                                  if empty defaults to $(POD_NAME).$(ETCD_CLUSTER_NAME).$(POD_NAMESPACE).svc.cluster.local
                                items:
                                  type: string
                                type: array
                              ipAddresses:
                                description: IPs is the expected array of IP address
                                  subject alternative names.
                                items:
                                  type: string
                                type: array
                            type: object
                          caBundleSecret:
                            description: |-
                              CABundleSecret is the expected secret name with CABundle present. It's used
                              by each etcd POD to verify TLS communications with its peers or clients. If it isn't
                              provided, the CA included in the secret generated by certificate provider will be
                              used instead if present; otherwise, there is no way to verify TLS communications.
                            type: string
                          commonName:
                            description: |-
                              CommonName is the expected common name X509 certificate subject attribute.
                              Should have a length of 64 characters or fewer to avoid generating invalid CSRs.
                            type: string
                          organizations:
                            description: Organization is the expected array of Organization
                              names to be used on the Certificate.
                            items:
                              type: string
                            type: array
                          validityDuration:
                            description: |-
                              ValidityDuration is the expected duration until which the certificate will be valid,
                              expects in human-readable duration: 100d12h, if empty defaults to 90d
                            type: string
                        type: object
                      certManagerCfg:
                        properties:
                          altNames:
                            description: |-
                              AltNames contains the domain names and IP addresses that will be added
                              to the x509 certificate SubAltNames fields. The values will be passed
                              directly to the x509.Certificate object.
                            properties:
                              dnsNames:
                                description: |-
                                  DNSNames is the expected array of DNS subject alternative names.
                                  if empty defaults to $(POD_NAME).$(ETCD_CLUSTER_NAME).$(POD_NAMESPACE).svc.cluster.local
                                items:
                                  type: string
                                type: array
                              ipAddresses:
                                description: IPs is the expected array of IP address
                                  subject alternative names.
                                items:
                                  type: string
                                type: array
                            type: object
                          caBundleSecret:
                            description: |-
                              CABundleSecret is the expected secret name with CABundle present. It's used
                              by each etcd POD to verify TLS communications with its peers or clients. If it isn't
                              provided, the CA included in the secret generated by certificate provider will be
                              used instead if present; otherwise, there is no way to verify TLS communications.
                            type: string
                          commonName:
                            description: |-
                              CommonName is the expected common name X509 certificate subject attribute.
                              Should have a length of 64 characters or fewer to avoid generating invalid CSRs.
                            type: string
                          issuerKind:
                            description: IssuerKind is the expected kind of Issuer,
                              either "ClusterIssuer" or "Issuer"
                            type: string
                          issuerName:
                            description: IssuerName is the expected name of Issuer
                              required to issue a certificate
                            type: string
                          organizations:
                            description: Organization is the expected array of Organization
                              names to be used on the Certificate.
                            items:
                              type: string
                            type: array
                          validityDuration:
                            description: |-
                              ValidityDuration is the expected duration until which the certificate will be valid,
                              expects in human-readable duration: 100d12h, if empty defaults to 90d
                            type: string
                        required:
                        - issuerKind
                        - issuerName
                        type: object
                    type: object
                type: object
              version:
                description: Version is the expected version of the etcd container
                  image.
                type: string
            required:
            - size
            - version
            type: object
          status:
            description: EtcdClusterStatus defines the observed state of EtcdCluster.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
