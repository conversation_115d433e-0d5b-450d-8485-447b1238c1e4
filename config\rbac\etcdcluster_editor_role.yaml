# permissions for end users to edit etcdclusters.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: etcd-operator
    app.kubernetes.io/managed-by: kustomize
  name: etcdcluster-editor-role
rules:
- apiGroups:
  - operator.etcd.io
  resources:
  - etcdclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - operator.etcd.io
  resources:
  - etcdclusters/status
  verbs:
  - get
