apiVersion: v1
kind: Namespace
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-operator
    control-plane: controller-manager
  name: etcd-operator-system
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.2
  name: etcdclusters.operator.etcd.io
spec:
  group: operator.etcd.io
  names:
    kind: EtcdCluster
    listKind: EtcdClusterList
    plural: etcdclusters
    singular: etcdcluster
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: EtcdCluster is the Schema for the etcdclusters API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: EtcdClusterSpec defines the desired state of EtcdCluster.
            properties:
              etcdOptions:
                description: etcd configuration options are passed as command line
                  arguments to the etcd container, refer to etcd documentation for
                  configuration options applicable for the version of etcd being used.
                items:
                  type: string
                type: array
              size:
                description: Size is the expected size of the etcd cluster.
                type: integer
              storageSpec:
                description: StorageSpec is the name of the StorageSpec to use for
                  the etcd cluster. If not provided, then each POD just uses the temporary
                  storage inside the container.
                properties:
                  accessModes:
                    type: string
                  pvcName:
                    type: string
                  storageClassName:
                    type: string
                  volumeSizeLimit:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  volumeSizeRequest:
                    anyOf:
                    - type: integer
                    - type: string
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                required:
                - volumeSizeRequest
                type: object
              tls:
                description: TLS is the TLS certificate configuration to use for the
                  etcd cluster and etcd operator.
                properties:
                  provider:
                    type: string
                  providerCfg:
                    properties:
                      autoCfg:
                        type: object
                      certManagerCfg:
                        type: object
                    type: object
                type: object
              version:
                description: Version is the expected version of the etcd container
                  image.
                type: string
            required:
            - size
            - version
            type: object
          status:
            description: EtcdClusterStatus defines the observed state of EtcdCluster.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-operator
  name: etcd-operator-controller-manager
  namespace: etcd-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-operator
  name: etcd-operator-leader-election-role
  namespace: etcd-operator-system
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-operator
  name: etcd-operator-etcdcluster-editor-role
rules:
- apiGroups:
  - operator.etcd.io
  resources:
  - etcdclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - operator.etcd.io
  resources:
  - etcdclusters/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-operator
  name: etcd-operator-etcdcluster-viewer-role
rules:
- apiGroups:
  - operator.etcd.io
  resources:
  - etcdclusters
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - operator.etcd.io
  resources:
  - etcdclusters/status
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: etcd-operator-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - get
  - list
  - patch
  - update
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - operator.etcd.io
  resources:
  - etcdclusters
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - operator.etcd.io
  resources:
  - etcdclusters/finalizers
  verbs:
  - update
- apiGroups:
  - operator.etcd.io
  resources:
  - etcdclusters/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: etcd-operator-metrics-auth-role
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: etcd-operator-metrics-reader
rules:
- nonResourceURLs:
  - /metrics
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-operator
  name: etcd-operator-leader-election-rolebinding
  namespace: etcd-operator-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: etcd-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: etcd-operator-controller-manager
  namespace: etcd-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-operator
  name: etcd-operator-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: etcd-operator-manager-role
subjects:
- kind: ServiceAccount
  name: etcd-operator-controller-manager
  namespace: etcd-operator-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: etcd-operator-metrics-auth-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: etcd-operator-metrics-auth-role
subjects:
- kind: ServiceAccount
  name: etcd-operator-controller-manager
  namespace: etcd-operator-system
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-operator
    control-plane: controller-manager
  name: etcd-operator-controller-manager-metrics-service
  namespace: etcd-operator-system
spec:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: 8443
  selector:
    control-plane: controller-manager
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/name: etcd-operator
    control-plane: controller-manager
  name: etcd-operator-controller-manager
  namespace: etcd-operator-system
spec:
  replicas: 1
  selector:
    matchLabels:
      control-plane: controller-manager
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
    spec:
      containers:
      - args:
        - --metrics-bind-address=:8443
        - --leader-elect
        - --health-probe-bind-address=:8081
        command:
        - /manager
        image: gcr.io/etcd-development/etcd-operator:v0.1.0
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: manager
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      securityContext:
        runAsNonRoot: true
      serviceAccountName: etcd-operator-controller-manager
      terminationGracePeriodSeconds: 10
