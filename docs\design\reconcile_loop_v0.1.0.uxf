<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.0.0">
  <zoom_level>10</zoom_level>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>750</x>
      <y>180</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>700</x>
      <y>1100</y>
      <w>120</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>type=decision
any member
unhealthy?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>920</x>
      <y>1070</y>
      <w>300</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>print error or warning log message, and
update metrics, based on which we can
generate alarms. We expect the error
should can be fixed automatically
or manually for now.</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>810</x>
      <y>1100</y>
      <w>130</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;20.0;110.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>190</y>
      <w>30</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>710</x>
      <y>1810</y>
      <w>100</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>type=decision
any learner?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Text</id>
    <coordinates>
      <x>740</x>
      <y>0</y>
      <w>680</w>
      <h>100</h>
    </coordinates>
    <panel_attributes>First version (v0.1.0): only support basic scale in &amp; out and upgrade.
Do not support:
    1. adding learners.
    2. downgrade
style=wordwrap
fontsize=20</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>870</x>
      <y>180</y>
      <w>270</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>Start of a reconcile loop, we should
return an error if any step fails, so
the obj will be requeued,
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>180</y>
      <w>130</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>110.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Text</id>
    <coordinates>
      <x>740</x>
      <y>110</y>
      <w>410</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>Note: when we add a member, we will still add it as a learner first, then promote it as a voting member later.
style=wordwrap
fontsize=14</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>920</x>
      <y>1800</y>
      <w>140</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>type=decision
Is the learner
in sync with
the leader?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>1130</y>
      <w>50</w>
      <h>340</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;320.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>1810</y>
      <w>140</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;20.0;120.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1150</x>
      <y>1810</y>
      <w>130</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>promote it to
a voting member</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1050</x>
      <y>1810</y>
      <w>120</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;20.0;100.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>980</x>
      <y>1850</y>
      <w>590</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0;570.0;70.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>750</x>
      <y>2750</y>
      <w>20</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>360</y>
      <w>810</w>
      <h>2420</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>60.0;10.0;790.0;10.0;790.0;2400.0;10.0;2400.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1270</x>
      <y>1820</y>
      <w>300</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;280.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>660</x>
      <y>2370</y>
      <w>210</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>remove the member that has
the biggest Ordinal Index</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>690</x>
      <y>2040</y>
      <w>140</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>type=decision
cluster size
expected?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>1840</y>
      <w>50</w>
      <h>220</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;200.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>690</x>
      <y>2150</y>
      <w>140</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>type=decision
size &lt; expected?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>2090</y>
      <w>50</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>820</x>
      <y>2160</y>
      <w>90</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;20.0;70.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>940</x>
      <y>2370</y>
      <w>140</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>add a learner
member</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>940</x>
      <y>940</y>
      <w>480</w>
      <h>110</h>
    </coordinates>
    <panel_attributes>We should exclude the member below,
1. which has been added into the cluster, but hasn't started yet
2. which has been removed from the cluster, but hasn't stopped yet.

Assuming `etcdctl member list` returns M members, 
we only check min(M, sts.replica) members.
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1000</x>
      <y>2410</y>
      <w>30</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>1130</x>
      <y>2370</y>
      <w>200</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>call MemberAddAsLearner
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>2200</y>
      <w>50</w>
      <h>190</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;170.0</additional_attributes>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>800</x>
      <y>2230</y>
      <w>130</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>call 
MemberRemove
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>830</x>
      <y>2270</y>
      <w>40</w>
      <h>120</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>20.0;10.0;10.0;100.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>2410</y>
      <w>30</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>890</x>
      <y>2150</y>
      <w>240</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>type=decision
PV &amp; PVC for the
new member (to be added)
already exist?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1200</x>
      <y>2120</y>
      <w>300</w>
      <h>120</h>
    </coordinates>
    <panel_attributes>If the retention policy is delete, then
previous scale In hasn't finished yet.
Otherwise if the retention policy is
retain, then we need to cleanup the
legacy PV &amp; PVC, which can be done by
a separate Garbage collection goroutine.
halign=left</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1120</x>
      <y>2160</y>
      <w>100</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;20.0;80.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1000</x>
      <y>2200</y>
      <w>50</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1070</x>
      <y>2380</y>
      <w>80</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>60.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>940</x>
      <y>840</y>
      <w>260</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>1. etcdctl member list
2. etcdctl endpoint status --cluster
3. get etcd PODs' status
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>860</x>
      <y>860</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>80.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>710</x>
      <y>1450</y>
      <w>100</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>type=decision
sts replica
expected?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>930</x>
      <y>1550</y>
      <w>140</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>type=decision
replica &gt; expected?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>940</x>
      <y>1280</y>
      <w>310</w>
      <h>140</h>
    </coordinates>
    <panel_attributes>We check whether statefulsets's replica
is equal to the member count, which is
returned from `etcdctl member list`.
Usually they should be equal. If it isn't,
then it means previous loop somehow
interrupted right after adding or removing
a member from the cluster. We should
finish the left work of previous loop.
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>1320</y>
      <w>200</w>
      <h>150</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>180.0;10.0;10.0;130.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>800</x>
      <y>1450</y>
      <w>140</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;20.0;120.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1150</x>
      <y>1560</y>
      <w>200</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>Decrease the Statefulset's
replica by 1</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1060</x>
      <y>1560</y>
      <w>110</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;20.0;90.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1340</x>
      <y>1570</y>
      <w>230</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;210.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>990</x>
      <y>1600</y>
      <w>50</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>920</x>
      <y>1450</y>
      <w>170</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>update configuration
for the cluster</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1080</x>
      <y>1460</y>
      <w>90</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>70.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>910</x>
      <y>1660</y>
      <w>200</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>Increase the Statefulset's
replica by 1</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>990</x>
      <y>1490</y>
      <w>30</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1100</x>
      <y>1670</y>
      <w>470</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;450.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>1480</y>
      <w>50</w>
      <h>350</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;330.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>970</y>
      <w>200</w>
      <h>150</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>180.0;10.0;10.0;130.0</additional_attributes>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>930</x>
      <y>2030</y>
      <w>350</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>check whether the actual member count (either
the sts's replica or returned by etcdctl member
list command) is equal to the expected member
count (saved in the CustomResource).
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>820</x>
      <y>2060</y>
      <w>130</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>110.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>910</x>
      <y>2590</y>
      <w>200</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>Increase the Statefulset's
replica by 1</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1100</x>
      <y>2600</y>
      <w>470</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;450.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>660</x>
      <y>2590</y>
      <w>200</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>decrease the Statefulset's
replica by 1</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>2630</y>
      <w>30</w>
      <h>140</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;120.0</additional_attributes>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>860</x>
      <y>2700</y>
      <w>190</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>end of a reconcile loop
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>2710</y>
      <w>120</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>100.0;10.0;10.0;40.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1490</x>
      <y>2170</y>
      <w>80</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;60.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>600</y>
      <w>50</w>
      <h>270</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;250.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>670</x>
      <y>850</y>
      <w>200</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>health check if replica &gt; 0
and update status</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>890</y>
      <w>30</w>
      <h>230</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;210.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>700</x>
      <y>350</y>
      <w>120</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>type=decision
CR exist?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>380</y>
      <w>50</w>
      <h>110</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;90.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1210</x>
      <y>1110</y>
      <w>360</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;340.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Text</id>
    <coordinates>
      <x>870</x>
      <y>350</y>
      <w>40</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>No
style=wordwrap</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>930</x>
      <y>280</y>
      <w>240</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>In case the CR is removed right
after it being created or updated.
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>810</x>
      <y>290</y>
      <w>140</w>
      <h>100</h>
    </coordinates>
    <panel_attributes>lt=&lt;.</panel_attributes>
    <additional_attributes>120.0;10.0;10.0;80.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>690</x>
      <y>250</y>
      <w>150</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>get the EtcdCluster 
resource again</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>290</y>
      <w>30</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>690</x>
      <y>470</y>
      <w>150</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>get the Statefulsets 
resource</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>700</x>
      <y>570</y>
      <w>120</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>type=decision
sts exist?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>510</y>
      <w>30</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>920</x>
      <y>570</y>
      <w>140</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>type=decision
ec.Spec.Size &gt; 0?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>810</x>
      <y>570</y>
      <w>130</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;20.0;110.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>920</x>
      <y>660</y>
      <w>150</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>create a Statefulsets 
with replica = 0</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>980</x>
      <y>600</y>
      <w>50</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1050</x>
      <y>570</y>
      <w>520</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;20.0;500.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>810</x>
      <y>670</y>
      <w>130</w>
      <h>200</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>110.0;10.0;10.0;10.0;10.0;180.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>940</x>
      <y>2270</y>
      <w>140</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>type=decision
first member?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1060</x>
      <y>2280</y>
      <w>330</w>
      <h>220</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>20.0;10.0;310.0;10.0;310.0;160.0;10.0;160.0;10.0;200.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1000</x>
      <y>2300</y>
      <w>50</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>920</x>
      <y>2480</y>
      <w>220</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>create or update configuration
for the cluster</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>1230</x>
      <y>2480</y>
      <w>210</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>populate --initial-cluster
and --initial-cluster-state
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Text</id>
    <coordinates>
      <x>1110</x>
      <y>2260</y>
      <w>50</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Yes
style=wordwrap</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>1150</x>
      <y>1450</y>
      <w>170</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>--initial-cluster
--initial-cluster-state
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>650</x>
      <y>2480</y>
      <w>220</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>update configuration
for the cluster</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>2520</y>
      <w>30</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1000</x>
      <y>2520</y>
      <w>30</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>420</x>
      <y>2040</y>
      <w>140</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>type=decision
version
changed?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>550</x>
      <y>2050</y>
      <w>160</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>140.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>420</x>
      <y>2170</y>
      <w>140</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>type=decision
upgrade case?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>100</x>
      <y>2060</y>
      <w>670</w>
      <h>720</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>320.0;10.0;10.0;10.0;10.0;700.0;650.0;700.0</additional_attributes>
  </element>
  <element>
    <id>Text</id>
    <coordinates>
      <x>360</x>
      <y>2030</y>
      <w>40</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>No
style=wordwrap</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>2090</y>
      <w>50</w>
      <h>100</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;80.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>170</x>
      <y>2180</y>
      <w>190</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>print error or warning:
downgrade not supported</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>350</x>
      <y>2180</y>
      <w>90</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>70.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>100</x>
      <y>2190</y>
      <w>90</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>70.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>420</x>
      <y>2300</y>
      <w>140</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>type=decision
valid upgrade?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>2220</y>
      <w>50</w>
      <h>100</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;80.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>180</x>
      <y>2310</y>
      <w>180</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>print error or warning</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>350</x>
      <y>2310</y>
      <w>90</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
No</panel_attributes>
    <additional_attributes>70.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>100</x>
      <y>2320</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>80.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>420</x>
      <y>2480</y>
      <w>150</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>update Statefulsets
spec directly</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>480</x>
      <y>2350</y>
      <w>50</w>
      <h>150</h>
    </coordinates>
    <panel_attributes>lt=-&gt;
Yes</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;130.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>100</x>
      <y>2490</y>
      <w>340</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-&gt;</panel_attributes>
    <additional_attributes>320.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLNote</id>
    <coordinates>
      <x>170</x>
      <y>2380</y>
      <w>220</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>Supported cases:
1. patch version upgrade of
   the same minor version
2. one minor version upgrade
bg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>60</x>
      <y>1980</y>
      <w>530</w>
      <h>810</h>
    </coordinates>
    <panel_attributes>lt=.
Upgrade
fontsize=24</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>640</x>
      <y>1980</y>
      <w>960</w>
      <h>810</h>
    </coordinates>
    <panel_attributes>lt=.
Scale Out &amp; In
fontsize=24</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>640</x>
      <y>1760</y>
      <w>960</w>
      <h>180</h>
    </coordinates>
    <panel_attributes>lt=.
Promote learner
fontsize=24</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>640</x>
      <y>1220</y>
      <w>960</w>
      <h>510</h>
    </coordinates>
    <panel_attributes>lt=.
Exception handling
fontsize=24</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>640</x>
      <y>780</y>
      <w>960</w>
      <h>410</h>
    </coordinates>
    <panel_attributes>lt=.
Health check
fontsize=24</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>640</x>
      <y>440</y>
      <w>960</w>
      <h>310</h>
    </coordinates>
    <panel_attributes>lt=.
Bootstrap Statefulsets
fontsize=24</panel_attributes>
    <additional_attributes/>
  </element>
</diagram>
