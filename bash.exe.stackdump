Stack trace:
Frame         Function      Args
0007FFFFB4E0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB4E0, 0007FFFFA3E0) msys-2.0.dll+0x1FE8E
0007FFFFB4E0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB7B8) msys-2.0.dll+0x67F9
0007FFFFB4E0  000210046832 (000210286019, 0007FFFFB398, 0007FFFFB4E0, 000000000000) msys-2.0.dll+0x6832
0007FFFFB4E0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB4E0  000210068E24 (0007FFFFB4F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFB7C0  00021006A225 (0007FFFFB4F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF490E0000 ntdll.dll
7FFF48CC0000 KERNEL32.DLL
7FFF45EB0000 KERNELBASE.dll
7FFF489F0000 USER32.dll
7FFF462F0000 win32u.dll
000210040000 msys-2.0.dll
7FFF48700000 GDI32.dll
7FFF46150000 gdi32full.dll
7FFF45180000 msvcp_win.dll
7FFF45A70000 ucrtbase.dll
7FFF47C20000 advapi32.dll
7FFF48540000 msvcrt.dll
7FFF47B80000 sechost.dll
7FFF485E0000 RPCRT4.dll
7FFF44AD0000 CRYPTBASE.DLL
7FFF45C20000 bcryptPrimitives.dll
7FFF48490000 IMM32.DLL
