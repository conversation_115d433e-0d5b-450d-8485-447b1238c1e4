version: 2
updates:
- package-ecosystem: "gomod"
  directory: "/"
  schedule:
    interval: "weekly"
  allow:
  - dependency-type: "direct"
  groups:
    k8s:
      patterns:
      - "k8s.io/*"
      - "sigs.k8s.io/*"
    onsi:
      patterns:
      - "github.com/onsi/*"
    etcd:
      patterns:
      - "go.etcd.io/*"

- package-ecosystem: gomod
  directory: /tools/mod # Not linked from /go.mod
  schedule:
    interval: weekly
  allow:
    - dependency-type: direct

- package-ecosystem: "docker"
  directory: "/"
  schedule:
    interval: "weekly"

- package-ecosystem: "github-actions"
  directory: "/"
  schedule:
    interval: "weekly"
